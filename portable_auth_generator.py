#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
便携授权码生成工具
用于生成限时便携授权码，不依赖机器码
"""

import hashlib
import hmac
import base64
import json
from datetime import datetime, timedelta
import sys
import os

# 尝试导入PySide6，如果失败则使用命令行模式
try:
    from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QWidget, QLabel, QPushButton, QMessageBox, QLineEdit,
                                QDateEdit, QTextEdit, QGroupBox, QFormLayout, QSpinBox)
    from PySide6.QtCore import QDate, Qt
    from PySide6.QtGui import QFont, QClipboard
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

class PortableAuthGenerator:
    def __init__(self):
        # 加密密钥和盐值（与验证端保持一致）
        self.secret_key = "MFChen_Portable_Auth_2024_Secret_Key"
        self.salt = "MFChen_Salt_2024"
        
    def _generate_signature(self, data_str):
        """生成数据签名"""
        return hmac.new(
            self.secret_key.encode(),
            data_str.encode(),
            hashlib.sha256
        ).hexdigest()[:16]  # 取前16位作为签名
    
    def generate_portable_auth_code(self, start_date, end_date, user_id=""):
        """
        生成便携授权码
        
        参数:
        start_date: 开始日期 (datetime对象或字符串 YYYY-MM-DD)
        end_date: 结束日期 (datetime对象或字符串 YYYY-MM-DD)
        user_id: 用户标识（可选）
        
        返回:
        便携授权码字符串
        """
        # 转换日期格式
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        
        # 验证日期范围
        if start_date >= end_date:
            raise ValueError("开始日期必须早于结束日期")
        
        # 检查时间范围是否超过7天
        if (end_date - start_date).days > 7:
            raise ValueError("便携授权码有效期不能超过7天")
        
        # 构建授权数据
        auth_data = {
            "start": start_date.strftime("%Y-%m-%d"),
            "end": end_date.strftime("%Y-%m-%d"),
            "user": user_id,
            "version": "1.0"
        }
        
        # 转换为JSON字符串
        data_str = json.dumps(auth_data, sort_keys=True, separators=(',', ':'))
        
        # 生成签名
        signature = self._generate_signature(data_str)
        
        # 组合最终数据
        final_data = {
            "data": auth_data,
            "sig": signature
        }
        
        # 转换为JSON并编码
        json_str = json.dumps(final_data, separators=(',', ':'))
        encoded = base64.b64encode(json_str.encode()).decode()
        
        # 添加前缀标识
        portable_code = f"PAC_{encoded}"
        
        return portable_code
    
    def verify_portable_auth_code(self, auth_code):
        """
        验证便携授权码（用于测试）
        
        返回: (是否有效, 授权信息字典, 错误信息)
        """
        try:
            # 检查前缀
            if not auth_code.startswith("PAC_"):
                return False, None, "无效的便携授权码格式"
            
            # 解码
            encoded_data = auth_code[4:]  # 去掉前缀
            json_str = base64.b64decode(encoded_data).decode()
            final_data = json.loads(json_str)
            
            # 提取数据和签名
            auth_data = final_data.get("data", {})
            signature = final_data.get("sig", "")
            
            # 验证签名
            data_str = json.dumps(auth_data, sort_keys=True, separators=(',', ':'))
            expected_signature = self._generate_signature(data_str)
            
            if signature != expected_signature:
                return False, None, "便携授权码签名验证失败"
            
            # 检查日期格式
            start_date = datetime.strptime(auth_data["start"], "%Y-%m-%d")
            end_date = datetime.strptime(auth_data["end"], "%Y-%m-%d")
            
            # 检查当前时间是否在有效期内
            current_date = datetime.now().date()
            if current_date < start_date.date():
                return False, auth_data, "便携授权码尚未生效"
            elif current_date > end_date.date():
                return False, auth_data, "便携授权码已过期"
            
            return True, auth_data, "便携授权码验证通过"
            
        except Exception as e:
            return False, None, f"便携授权码解析错误: {str(e)}"

class PortableAuthGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.generator = PortableAuthGenerator()
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("便携授权码生成工具")
        self.setGeometry(300, 300, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("便携授权码生成工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 生成区域
        generate_group = QGroupBox("生成便携授权码")
        generate_layout = QFormLayout(generate_group)
        
        # 开始日期
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        generate_layout.addRow("开始日期:", self.start_date)
        
        # 结束日期
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(3))  # 默认3天
        self.end_date.setCalendarPopup(True)
        generate_layout.addRow("结束日期:", self.end_date)
        
        # 用户标识
        self.user_id = QLineEdit()
        self.user_id.setPlaceholderText("可选，用于标识用户")
        generate_layout.addRow("用户标识:", self.user_id)
        
        # 生成按钮
        generate_btn = QPushButton("生成便携授权码")
        generate_btn.clicked.connect(self.generate_code)
        generate_layout.addRow(generate_btn)
        
        layout.addWidget(generate_group)
        
        # 结果显示区域
        result_group = QGroupBox("生成结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(100)
        result_layout.addWidget(self.result_text)
        
        # 复制按钮
        copy_btn = QPushButton("复制到剪贴板")
        copy_btn.clicked.connect(self.copy_to_clipboard)
        result_layout.addWidget(copy_btn)
        
        layout.addWidget(result_group)
        
        # 验证区域
        verify_group = QGroupBox("验证便携授权码")
        verify_layout = QVBoxLayout(verify_group)
        
        self.verify_input = QLineEdit()
        self.verify_input.setPlaceholderText("输入便携授权码进行验证")
        verify_layout.addWidget(self.verify_input)
        
        verify_btn = QPushButton("验证授权码")
        verify_btn.clicked.connect(self.verify_code)
        verify_layout.addWidget(verify_btn)
        
        self.verify_result = QTextEdit()
        self.verify_result.setReadOnly(True)
        self.verify_result.setMaximumHeight(80)
        verify_layout.addWidget(self.verify_result)
        
        layout.addWidget(verify_group)
    
    def generate_code(self):
        """生成便携授权码"""
        try:
            start_date = self.start_date.date().toPython()
            end_date = self.end_date.date().toPython()
            user_id = self.user_id.text().strip()
            
            # 验证日期
            if start_date >= end_date:
                QMessageBox.warning(self, "错误", "开始日期必须早于结束日期！")
                return
            
            if (end_date - start_date).days > 7:
                QMessageBox.warning(self, "错误", "便携授权码有效期不能超过7天！")
                return
            
            # 生成授权码
            auth_code = self.generator.generate_portable_auth_code(start_date, end_date, user_id)
            
            # 显示结果
            result_info = f"便携授权码生成成功！\n"
            result_info += f"有效期: {start_date} 至 {end_date}\n"
            result_info += f"用户: {user_id if user_id else '未指定'}\n"
            result_info += f"授权码: {auth_code}"
            
            self.result_text.setPlainText(result_info)
            
            QMessageBox.information(self, "成功", "便携授权码生成成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成失败: {str(e)}")
    
    def copy_to_clipboard(self):
        """复制到剪贴板"""
        text = self.result_text.toPlainText()
        if text:
            # 提取授权码部分
            lines = text.split('\n')
            auth_code = ""
            for line in lines:
                if line.startswith("授权码: "):
                    auth_code = line.replace("授权码: ", "")
                    break
            
            if auth_code:
                clipboard = QApplication.clipboard()
                clipboard.setText(auth_code)
                QMessageBox.information(self, "成功", "便携授权码已复制到剪贴板！")
            else:
                QMessageBox.warning(self, "警告", "没有找到可复制的授权码！")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的内容！")
    
    def verify_code(self):
        """验证便携授权码"""
        auth_code = self.verify_input.text().strip()
        if not auth_code:
            QMessageBox.warning(self, "警告", "请输入便携授权码！")
            return
        
        valid, auth_data, message = self.generator.verify_portable_auth_code(auth_code)
        
        result_text = f"验证结果: {message}\n"
        if auth_data:
            result_text += f"开始日期: {auth_data.get('start', 'N/A')}\n"
            result_text += f"结束日期: {auth_data.get('end', 'N/A')}\n"
            result_text += f"用户标识: {auth_data.get('user', 'N/A')}\n"
        
        self.verify_result.setPlainText(result_text)

def main():
    """主函数"""
    if PYSIDE6_AVAILABLE:
        # GUI模式
        app = QApplication(sys.argv)
        window = PortableAuthGeneratorGUI()
        window.show()
        return app.exec()
    else:
        # 命令行模式
        print("便携授权码生成工具（命令行模式）")
        print("=" * 50)
        
        generator = PortableAuthGenerator()
        
        try:
            # 获取用户输入
            start_date_str = input("请输入开始日期 (YYYY-MM-DD): ").strip()
            end_date_str = input("请输入结束日期 (YYYY-MM-DD): ").strip()
            user_id = input("请输入用户标识 (可选): ").strip()
            
            # 生成授权码
            auth_code = generator.generate_portable_auth_code(start_date_str, end_date_str, user_id)
            
            print("\n" + "=" * 50)
            print("便携授权码生成成功！")
            print(f"有效期: {start_date_str} 至 {end_date_str}")
            print(f"用户: {user_id if user_id else '未指定'}")
            print(f"授权码: {auth_code}")
            print("=" * 50)
            
        except Exception as e:
            print(f"错误: {e}")
            return 1
        
        return 0

if __name__ == "__main__":
    sys.exit(main())
