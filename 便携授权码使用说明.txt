便携授权码功能使用说明
==========================

概述
----
便携授权码是一个临时离线限时加密功能，为未授权用户提供短期使用权限。
该功能不依赖机器码，支持动态时间范围设置，并具有时间回退检测功能。

主要特性
--------
1. 独立于机器码的临时授权方式
2. 限时使用（最长7天）
3. 支持多个授权码的续期功能
4. 时间回退检测，防止时间篡改
5. 加密签名验证，防止伪造
6. GUI和命令行双模式支持

文件结构
--------
- portable_auth_generator.py    # 便携授权码生成工具
- portable_auth_verifier.py     # 便携授权码验证器
- machine_code_verifier.py      # 增强的授权验证系统（已修改）
- test_portable_auth.py         # 功能测试脚本
- test_auth_dialog.py           # 授权对话框测试脚本

使用方法
--------

1. 生成便携授权码
   运行: python portable_auth_generator.py
   - GUI模式：提供图形界面，可设置日期范围和用户标识
   - 命令行模式：在没有PySide6时自动切换到命令行模式
   
   生成的授权码格式：PAC_[base64编码数据]
   示例：PAC_eyJkYXRhIjp7InN0YXJ0IjoiMjAyNS0wNy0xOCIsImVuZCI6IjIwMjUtMDctMjEi...

2. 用户使用便携授权码
   - 当软件提示未授权时，会显示授权对话框
   - 对话框中包含机器码显示和便携授权码输入区域
   - 用户输入便携授权码并点击"验证便携授权码"
   - 验证成功后可选择"启动应用"

3. 管理员功能
   - 可以为同一用户生成多个时间段的授权码
   - 支持续期功能（如先给20250719-20250721，后续再给20250725-20250730）
   - 每个授权码最长有效期7天
   - 授权码具有唯一性，防止重复激活

技术实现
--------

1. 加密机制
   - 使用HMAC-SHA256进行数据签名
   - Base64编码传输
   - 固定密钥和盐值确保一致性

2. 时间验证
   - 本地时间验证，不依赖网络
   - 时间回退检测（超过12小时触发失效）
   - 首次使用时间记录
   - 防止快速修改时间多次使用

3. 数据存储
   - 便携授权码记录：~/.mfchen_portable_auth_[应用名].dat
   - 时间记录文件：~/.mfchen_portable_time_[应用名].dat
   - 数据加密存储，防止篡改

4. 集成方式
   - 与现有机器码授权系统无缝集成
   - 优先检查机器码授权，失败后检查便携授权码
   - 不影响原有授权流程

安全特性
--------
1. 签名验证：防止授权码伪造
2. 时间限制：最长7天有效期
3. 时间回退检测：防止时间篡改
4. 唯一性验证：防止重复使用
5. 加密存储：本地数据加密保护

测试方法
--------
1. 功能测试：python test_portable_auth.py
2. 对话框测试：python test_auth_dialog.py
3. 生成工具测试：python portable_auth_generator.py

注意事项
--------
1. 便携授权码最长有效期为7天，超过视为可能被破解
2. 时间回退超过12小时会导致所有便携授权码失效
3. 授权码具有唯一性，同一授权码只能激活一次
4. 支持多个授权码的续期，但每个授权码独立验证
5. 本地时间验证，不依赖网络连接

故障排除
--------
1. 如果提示"便携授权码验证模块未找到"，请确保portable_auth_verifier.py文件存在
2. 如果时间验证失败，检查系统时间是否被回调
3. 如果授权码格式错误，请确保完整复制授权码（包含PAC_前缀）
4. 如果GUI无法启动，会自动切换到命令行模式

更新日志
--------
v1.0 (2025-07-18)
- 初始版本发布
- 支持便携授权码生成和验证
- 集成到现有授权系统
- 添加时间回退检测
- 支持GUI和命令行双模式
